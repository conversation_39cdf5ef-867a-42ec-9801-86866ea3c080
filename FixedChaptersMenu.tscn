[gd_scene load_steps=3 format=3 uid="uid://fixed_chapters_menu"]

[ext_resource type="Script" path="res://FixedChaptersMenu.gd" id="1_fixed"]
[ext_resource type="Texture2D" uid="uid://bebx3tdqwiil" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/BQ.png" id="2_bg"]

[node name="FixedChaptersMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_fixed")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("2_bg")
expand_mode = 1
stretch_mode = 6

[node name="MainContainer" type="MarginContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme_override_constants/margin_left = 40
theme_override_constants/margin_top = 40
theme_override_constants/margin_right = 40
theme_override_constants/margin_bottom = 40

[node name="VBoxContainer" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
theme_override_constants/separation = 30

[node name="HeaderSection" type="VBoxContainer" parent="MainContainer/VBoxContainer"]
layout_mode = 2
theme_override_constants/separation = 15

[node name="TitleLabel" type="Label" parent="MainContainer/VBoxContainer/HeaderSection"]
layout_mode = 2
theme_override_font_sizes/font_size = 42
text = "📚 KAPITOLY"
horizontal_alignment = 1

[node name="MessageLabel" type="Label" parent="MainContainer/VBoxContainer/HeaderSection"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "Chapters menu funguje!
Teraz môžeme pridať funkcionalitu."
horizontal_alignment = 1

[node name="ButtonsSection" type="HBoxContainer" parent="MainContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 4
theme_override_constants/separation = 30

[node name="BackButton" type="Button" parent="MainContainer/VBoxContainer/ButtonsSection"]
layout_mode = 2
theme_override_font_sizes/font_size = 22
text = "⬅️ Späť do Menu"
