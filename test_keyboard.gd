extends Control

@onready var test_input: LineEdit = $VBoxContainer/TestInput
var virtual_keyboard: VirtualKeyboard

func _ready():
	create_virtual_keyboard()
	test_input.focus_entered.connect(_on_input_focus_entered)

func create_virtual_keyboard():
	var keyboard_scene = preload("res://scenes/VirtualKeyboard.tscn")
	virtual_keyboard = keyboard_scene.instantiate()
	add_child(virtual_keyboard)
	
	# <PERSON><PERSON><PERSON><PERSON><PERSON> sign<PERSON><PERSON>
	virtual_keyboard.key_pressed.connect(_on_virtual_key_pressed)
	virtual_keyboard.backspace_pressed.connect(_on_virtual_backspace_pressed)
	virtual_keyboard.space_pressed.connect(_on_virtual_space_pressed)
	virtual_keyboard.enter_pressed.connect(_on_virtual_enter_pressed)

func _on_virtual_key_pressed(key: String):
	if test_input:
		test_input.text += key

func _on_virtual_backspace_pressed():
	if test_input and test_input.text.length() > 0:
		test_input.text = test_input.text.substr(0, test_input.text.length() - 1)

func _on_virtual_space_pressed():
	if test_input:
		test_input.text += " "

func _on_virtual_enter_pressed():
	print("Enter pressed, text: ", test_input.text)

func _on_input_focus_entered():
	if virtual_keyboard:
		virtual_keyboard.show_keyboard(test_input)
