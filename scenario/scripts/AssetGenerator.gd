extends Control

# UI References
@onready var category_option = $VBoxContainer/CategoryContainer/CategoryOption
@onready var subcategory_option = $VBoxContainer/SubcategoryContainer/SubcategoryOption
@onready var custom_prompt_input = $VBoxContainer/CustomContainer/CustomPromptInput
@onready var generate_button = $VBoxContainer/ButtonContainer/GenerateButton
@onready var preview_texture = $VBoxContainer/PreviewContainer/PreviewTexture
@onready var status_label = $VBoxContainer/StatusLabel
@onready var asset_list = $VBoxContainer/AssetListContainer/AssetList

# Asset categories
var categories = {
	"Pozadia": {
		"Hlavné menu": "main_menu_background",
		"Kapitola 1": "chapter_1",
		"Kapitola 2": "chapter_2", 
		"Kapitola 3": "chapter_3",
		"Kapitola 4": "chapter_4",
		"Kapitola 5": "chapter_5",
		"Kapitola 6": "chapter_6",
		"Kapitola 7": "chapter_7"
	},
	"Portréty": {
		"Van Helsing": "van_helsing",
		"Dracula": "dracula",
		"Mina": "mina",
		"Renfield": "renfield",
		"<PERSON>": "lucy"
	},
	"UI Elementy": {
		"Tlačidlo normálne": "button_normal",
		"Tlačidlo hover": "button_hover",
		"Panel": "panel",
		"Rámček": "frame",
		"Oddeľovač": "divider"
	},
	"Puzzle Elementy": {
		"Šifra": "cipher",
		"Pamäť": "memory",
		"Hádanka": "riddle",
		"Navigácia": "navigation",
		"Rituál": "ritual"
	}
}

func _ready():
	setup_ui()
	connect_signals()
	load_asset_list()

func setup_ui():
	"""Setup UI elements"""
	# Populate category dropdown
	for category in categories.keys():
		category_option.add_item(category)
	
	# Set initial state
	_on_category_changed(0)
	status_label.text = "Vyberte kategóriu a typ assetu"
	
	# Setup preview
	preview_texture.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
	preview_texture.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED

func connect_signals():
	"""Connect UI signals"""
	category_option.item_selected.connect(_on_category_changed)
	subcategory_option.item_selected.connect(_on_subcategory_changed)
	generate_button.pressed.connect(_on_generate_pressed)
	asset_list.item_selected.connect(_on_asset_selected)
	
	# Connect API signals
	ScenarioAPI.image_generated.connect(_on_image_generated)
	ScenarioAPI.generation_failed.connect(_on_generation_failed)

func _on_category_changed(index: int):
	"""Handle category selection change"""
	subcategory_option.clear()
	
	var category_name = category_option.get_item_text(index)
	var subcategories = categories.get(category_name, {})
	
	for subcategory in subcategories.keys():
		subcategory_option.add_item(subcategory)
	
	if subcategory_option.get_item_count() > 0:
		subcategory_option.selected = 0
		_on_subcategory_changed(0)

func _on_subcategory_changed(index: int):
	"""Handle subcategory selection change"""
	var category_name = category_option.get_item_text(category_option.selected)
	var subcategory_name = subcategory_option.get_item_text(index)
	
	status_label.text = "Pripravený na generovanie: " + category_name + " - " + subcategory_name

func _on_generate_pressed():
	"""Generate selected asset"""
	if category_option.selected < 0 or subcategory_option.selected < 0:
		status_label.text = "❌ Vyberte kategóriu a typ assetu"
		return
	
	generate_button.disabled = true
	status_label.text = "🎨 Generujem asset..."
	
	var category_name = category_option.get_item_text(category_option.selected)
	var subcategory_name = subcategory_option.get_item_text(subcategory_option.selected)
	var asset_type = categories[category_name][subcategory_name]
	
	# Generate based on category
	match category_name:
		"Pozadia":
			if asset_type == "main_menu_background":
				ScenarioAPI.generate_main_menu_background()
			else:
				var chapter_num = int(asset_type.split("_")[1])
				ScenarioAPI.generate_chapter_background(chapter_num)
		
		"Portréty":
			ScenarioAPI.generate_character_portrait(asset_type)
		
		"UI Elementy":
			ScenarioAPI.generate_ui_element(asset_type)
		
		"Puzzle Elementy":
			ScenarioAPI.generate_puzzle_element(asset_type)
		
		_:
			# Custom prompt
			var custom_prompt = custom_prompt_input.text
			if custom_prompt.is_empty():
				custom_prompt = "Gothic dark fantasy asset"
			
			ScenarioAPI.generate_image(custom_prompt, "custom_asset.png", "gothic")

func _on_image_generated(image_data: PackedByteArray, filename: String):
	"""Handle successful generation"""
	status_label.text = "✅ Asset vygenerovaný: " + filename
	generate_button.disabled = false
	
	# Load and display preview
	load_preview(filename)
	
	# Refresh asset list
	load_asset_list()

func _on_generation_failed(error_message: String):
	"""Handle generation failure"""
	status_label.text = "❌ Chyba: " + error_message
	generate_button.disabled = false

func load_preview(filename: String):
	"""Load generated asset as preview"""
	var file_path = "res://assets/generated/" + filename
	
	if FileAccess.file_exists(file_path):
		var texture = load(file_path)
		if texture:
			preview_texture.texture = texture
			print("🖼️ Preview načítaný: ", filename)

func load_asset_list():
	"""Load list of generated assets"""
	asset_list.clear()
	
	var assets_dir = "res://assets/generated/"
	if not DirAccess.dir_exists_absolute(assets_dir):
		return
	
	var dir = DirAccess.open(assets_dir)
	if not dir:
		return
	
	var files = []
	dir.list_dir_begin()
	var file_name = dir.get_next()
	
	while file_name != "":
		if file_name.ends_with(".png"):
			files.append(file_name)
		file_name = dir.get_next()
	
	# Sort files
	files.sort()
	
	# Add to list
	for file in files:
		asset_list.add_item(file)
	
	status_label.text += " (" + str(files.size()) + " assetov)"

func _on_asset_selected(index: int):
	"""Handle asset selection from list"""
	var filename = asset_list.get_item_text(index)
	load_preview(filename)

# Batch generation functions
func generate_all_backgrounds():
	"""Generate all chapter backgrounds"""
	status_label.text = "🎨 Generujem všetky pozadia..."
	
	ScenarioAPI.generate_main_menu_background()
	
	for i in range(1, 8):
		await get_tree().create_timer(2.0).timeout
		ScenarioAPI.generate_chapter_background(i)

func generate_all_portraits():
	"""Generate all character portraits"""
	status_label.text = "🎨 Generujem všetky portréty..."
	
	var characters = ["van_helsing", "dracula", "mina", "renfield", "lucy"]
	
	for character in characters:
		ScenarioAPI.generate_character_portrait(character)
		await get_tree().create_timer(2.0).timeout

func generate_all_ui_elements():
	"""Generate all UI elements"""
	status_label.text = "🎨 Generujem všetky UI elementy..."
	
	var ui_types = ["button_normal", "button_hover", "panel", "frame", "divider"]
	
	for ui_type in ui_types:
		ScenarioAPI.generate_ui_element(ui_type)
		await get_tree().create_timer(2.0).timeout
