extends Control

# UI References
@onready var background = $Background
@onready var title_label = $TitleFrame/TitleLabel
@onready var van_helsing_portrait = $VanHelsingPortrait
@onready var main_panel = $MainPanel
@onready var nova_hra_button = $MainPanel/ButtonContainer/NovaHraButton
@onready var kapitoly_button = $MainPanel/ButtonContainer/KapitolyButton
@onready var nastavenia_button = $MainPanel/ButtonContainer/NastaveniaButton
@onready var o_hre_button = $MainPanel/ButtonContainer/OHreButton
@onready var top_decoration = $TopDecoration
@onready var bottom_decoration = $BottomDecoration
@onready var status_label = $StatusLabel
@onready var progress_bar = $ProgressBar

# Generation state
var assets_generated = false
var generation_in_progress = false

func _ready():
	setup_ui()
	connect_signals()
	check_existing_assets()

func setup_ui():
	"""Setup initial UI state"""
	title_label.text = "PREKLIATE DEDIČSTVO"
	status_label.text = "Van Helsing: Prekliate Dedičstvo - Dark Anime Edition"
	progress_bar.visible = false

	# Load generated assets
	load_generated_assets()

	# Enable game buttons (no need for asset generation)
	set_game_buttons_enabled(true)

func connect_signals():
	"""Connect button signals"""
	nova_hra_button.pressed.connect(_on_nova_hra_pressed)
	kapitoly_button.pressed.connect(_on_kapitoly_pressed)
	nastavenia_button.pressed.connect(_on_nastavenia_pressed)
	o_hre_button.pressed.connect(_on_o_hre_pressed)

func check_existing_assets():
	"""Check if assets are already generated"""
	var assets_dir = "res://assets/generated/"
	
	if DirAccess.dir_exists_absolute(assets_dir):
		var dir = DirAccess.open(assets_dir)
		if dir:
			var files = []
			dir.list_dir_begin()
			var file_name = dir.get_next()
			
			while file_name != "":
				if file_name.ends_with(".png"):
					files.append(file_name)
				file_name = dir.get_next()
			
			if files.size() > 0:
				assets_generated = true
				load_generated_assets()
				set_game_buttons_enabled(true)
				status_label.text = "Assety načítané (" + str(files.size()) + " súborov)"

func load_generated_assets():
	"""Load and apply generated assets"""
	var bg_path = "res://assets/generated/menu_background_main.png"

	if FileAccess.file_exists(bg_path):
		var texture = load(bg_path)
		if texture:
			background.texture = texture
			background.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_COVERED
			print("✅ Pozadie načítané: ", bg_path)
			assets_generated = true
			status_label.text = "Úžasné dark anime main menu pripravené!"
	else:
		print("⚠️ Pozadie nenájdené, používam predvolené")
		status_label.text = "Spustite generovanie assetov pre kompletný vzhľad"

func set_game_buttons_enabled(enabled: bool):
	"""Enable/disable game navigation buttons"""
	nova_hra_button.disabled = not enabled
	kapitoly_button.disabled = not enabled
	nastavenia_button.disabled = not enabled
	o_hre_button.disabled = not enabled

# Game navigation functions
func _on_nova_hra_pressed():
	status_label.text = "Spúšťam novú hru..."
	print("Nová hra")

func _on_kapitoly_pressed():
	status_label.text = "Otváram výber kapitol..."
	print("Kapitoly")

func _on_nastavenia_pressed():
	status_label.text = "Otváram nastavenia..."
	print("Nastavenia")

func _on_o_hre_pressed():
	status_label.text = "Otváram informácie o hre..."
	print("O hre")
