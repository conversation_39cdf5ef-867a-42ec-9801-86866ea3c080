[gd_scene load_steps=2 format=3 uid="uid://asset_generator"]

[ext_resource type="Script" path="res://scripts/AssetGenerator.gd" id="1_script"]

[node name="AssetGenerator" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_script")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.05, 0.02, 0.1, 1)

[node name="ScrollContainer" type="ScrollContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="TitleLabel" type="Label" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
theme_override_constants/outline_size = 2
theme_override_font_sizes/font_size = 28
text = "🎨 Scenario Asset Generator"
horizontal_alignment = 1

[node name="Spacer1" type="Control" parent="ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="CategoryContainer" type="HBoxContainer" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2

[node name="CategoryLabel" type="Label" parent="ScrollContainer/VBoxContainer/CategoryContainer"]
custom_minimum_size = Vector2(120, 0)
layout_mode = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_font_sizes/font_size = 16
text = "Kategória:"
vertical_alignment = 1

[node name="CategoryOption" type="OptionButton" parent="ScrollContainer/VBoxContainer/CategoryContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_font_sizes/font_size = 16

[node name="SubcategoryContainer" type="HBoxContainer" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2

[node name="SubcategoryLabel" type="Label" parent="ScrollContainer/VBoxContainer/SubcategoryContainer"]
custom_minimum_size = Vector2(120, 0)
layout_mode = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_font_sizes/font_size = 16
text = "Typ:"
vertical_alignment = 1

[node name="SubcategoryOption" type="OptionButton" parent="ScrollContainer/VBoxContainer/SubcategoryContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_font_sizes/font_size = 16

[node name="Spacer2" type="Control" parent="ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 10)
layout_mode = 2

[node name="CustomContainer" type="VBoxContainer" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2

[node name="CustomLabel" type="Label" parent="ScrollContainer/VBoxContainer/CustomContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_font_sizes/font_size = 16
text = "Vlastný prompt (voliteľné):"

[node name="CustomPromptInput" type="TextEdit" parent="ScrollContainer/VBoxContainer/CustomContainer"]
custom_minimum_size = Vector2(0, 80)
layout_mode = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/background_color = Color(0.1, 0.05, 0.15, 0.8)
theme_override_font_sizes/font_size = 14
placeholder_text = "Napríklad: 'Temný gotický hrad s mesiacom v pozadí'"
wrap_mode = 1

[node name="Spacer3" type="Control" parent="ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="ButtonContainer" type="HBoxContainer" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2

[node name="GenerateButton" type="Button" parent="ScrollContainer/VBoxContainer/ButtonContainer"]
custom_minimum_size = Vector2(200, 50)
layout_mode = 2
size_flags_horizontal = 4
theme_override_colors/font_color = Color(1, 0.9, 0.7, 1)
theme_override_colors/font_hover_color = Color(1, 1, 0.8, 1)
theme_override_font_sizes/font_size = 18
text = "🎨 Generovať Asset"

[node name="Spacer4" type="Control" parent="ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="PreviewContainer" type="VBoxContainer" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2

[node name="PreviewLabel" type="Label" parent="ScrollContainer/VBoxContainer/PreviewContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_font_sizes/font_size = 18
text = "📷 Náhľad:"

[node name="PreviewTexture" type="TextureRect" parent="ScrollContainer/VBoxContainer/PreviewContainer"]
custom_minimum_size = Vector2(300, 200)
layout_mode = 2
size_flags_horizontal = 4

[node name="Spacer5" type="Control" parent="ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="AssetListContainer" type="VBoxContainer" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2

[node name="AssetListLabel" type="Label" parent="ScrollContainer/VBoxContainer/AssetListContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_font_sizes/font_size = 18
text = "📁 Vygenerované assety:"

[node name="AssetList" type="ItemList" parent="ScrollContainer/VBoxContainer/AssetListContainer"]
custom_minimum_size = Vector2(0, 150)
layout_mode = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/background = Color(0.1, 0.05, 0.15, 0.8)
theme_override_font_sizes/font_size = 14

[node name="Spacer6" type="Control" parent="ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="StatusLabel" type="Label" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0.8, 0.7, 0.5, 1)
theme_override_colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
theme_override_constants/outline_size = 1
theme_override_font_sizes/font_size = 16
text = "Pripravený na generovanie assetov"
horizontal_alignment = 1
