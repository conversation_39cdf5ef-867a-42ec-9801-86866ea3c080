[gd_scene load_steps=12 format=3 uid="uid://bqxvn8ywqhqxr"]

[ext_resource type="Script" path="res://scripts/EnhancedMainMenu.gd" id="1_script"]
[ext_resource type="Theme" uid="uid://2g01d2kieu86" path="res://themes/GothicTheme.tres" id="2_theme"]
[ext_resource type="Texture2D" path="res://assets/useful_ui/decorative_frames/layer_87.png" id="3_main_frame"]
[ext_resource type="Texture2D" path="res://assets/useful_ui/decorative_frames/layer_88.png" id="4_title_frame"]
[ext_resource type="Texture2D" path="res://assets/useful_ui/decorative_frames/layer_89.png" id="5_menu_frame"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/BQ.png" id="6_background"]
[ext_resource type="Texture2D" path="res://assets/logo.png" id="7_logo"]
[ext_resource type="FontFile" path="res://fonts/Cinzel,Cormorant_Garamond,Linden_Hill/Cinzel/static/Cinzel-Regular.ttf" id="8_font"]


[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_main_frame"]
texture = ExtResource("3_main_frame")
texture_margin_left = 32.0
texture_margin_top = 32.0
texture_margin_right = 32.0
texture_margin_bottom = 32.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_title_frame"]
texture = ExtResource("4_title_frame")
texture_margin_left = 24.0
texture_margin_top = 24.0
texture_margin_right = 24.0
texture_margin_bottom = 24.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_menu_frame"]
texture = ExtResource("5_menu_frame")
texture_margin_left = 20.0
texture_margin_top = 20.0
texture_margin_right = 20.0
texture_margin_bottom = 20.0

[sub_resource type="Animation" id="Animation_fade_in"]
resource_name = "fade_in"
length = 2.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}

[node name="EnhancedMainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme = ExtResource("2_theme")
script = ExtResource("1_script")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("6_background")
stretch_mode = 1

[node name="MainContainer" type="MarginContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme_override_constants/margin_left = 40
theme_override_constants/margin_top = 40
theme_override_constants/margin_right = 40
theme_override_constants/margin_bottom = 40
theme_override_styles/panel = SubResource("StyleBoxTexture_main_frame")

[node name="VBoxContainer" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
theme_override_constants/separation = 35

[node name="TitleSection" type="MarginContainer" parent="MainContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 0
theme_override_constants/margin_left = 20
theme_override_constants/margin_top = 20
theme_override_constants/margin_right = 20
theme_override_constants/margin_bottom = 20
theme_override_styles/panel = SubResource("StyleBoxTexture_title_frame")

[node name="TitleVBox" type="VBoxContainer" parent="MainContainer/VBoxContainer/TitleSection"]
layout_mode = 2
theme_override_constants/separation = 20

[node name="TitleLabel" type="Label" parent="MainContainer/VBoxContainer/TitleSection/TitleVBox"]
layout_mode = 2
theme_override_fonts/font = ExtResource("8_font")
theme_override_font_sizes/font_size = 48
theme_override_colors/font_color = Color(0.831, 0.686, 0.216, 1)
theme_override_colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
theme_override_constants/outline_size = 2
text = "PREKLIATE DEDIČSTVO"
horizontal_alignment = 1

[node name="SubtitleLabel" type="Label" parent="MainContainer/VBoxContainer/TitleSection/TitleVBox"]
layout_mode = 2
theme_override_fonts/font = ExtResource("8_font")
theme_override_font_sizes/font_size = 22
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
text = "Van Helsing: Gotická dobrodružná hra"
horizontal_alignment = 1

[node name="VersionLabel" type="Label" parent="MainContainer/VBoxContainer/TitleSection/TitleVBox"]
layout_mode = 2
theme_override_fonts/font = ExtResource("8_font")
theme_override_font_sizes/font_size = 14
theme_override_colors/font_color = Color(0.7, 0.6, 0.5, 1)
text = "verzia 1.0.0"
horizontal_alignment = 1

[node name="Spacer" type="Control" parent="MainContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
custom_minimum_size = Vector2(0, 150)

[node name="MenuSection" type="MarginContainer" parent="MainContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 0
theme_override_constants/margin_left = 20
theme_override_constants/margin_top = 20
theme_override_constants/margin_right = 20
theme_override_constants/margin_bottom = 20
theme_override_styles/panel = SubResource("StyleBoxTexture_menu_frame")

[node name="MenuVBox" type="VBoxContainer" parent="MainContainer/VBoxContainer/MenuSection"]
layout_mode = 2
theme_override_constants/separation = 30

[node name="ButtonsCenterContainer" type="CenterContainer" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox"]
layout_mode = 2

[node name="ButtonsContainer" type="VBoxContainer" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer"]
layout_mode = 2
theme_override_constants/separation = 25

[node name="NovaHraButton" type="Button" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(320, 60)
theme_override_fonts/font = ExtResource("8_font")
theme_override_font_sizes/font_size = 24
theme_override_colors/font_color = Color(0.95, 0.9, 0.8, 1)
theme_override_colors/font_hover_color = Color(1, 0.98, 0.9, 1)
theme_override_colors/font_outline_color = Color(0.15, 0.08, 0.03, 1)
theme_override_constants/outline_size = 1
text = "NOVÁ HRA"

[node name="PokracovatButton" type="Button" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(320, 60)
theme_override_fonts/font = ExtResource("8_font")
theme_override_font_sizes/font_size = 24
theme_override_colors/font_color = Color(0.95, 0.9, 0.8, 1)
theme_override_colors/font_hover_color = Color(1, 0.98, 0.9, 1)
theme_override_colors/font_outline_color = Color(0.15, 0.08, 0.03, 1)
theme_override_constants/outline_size = 1
text = "POKRAČOVAŤ"

[node name="KapitolyButton" type="Button" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(320, 60)
theme_override_fonts/font = ExtResource("8_font")
theme_override_font_sizes/font_size = 24
theme_override_colors/font_color = Color(0.95, 0.9, 0.8, 1)
theme_override_colors/font_hover_color = Color(1, 0.98, 0.9, 1)
theme_override_colors/font_outline_color = Color(0.15, 0.08, 0.03, 1)
theme_override_constants/outline_size = 1
text = "KAPITOLY"

[node name="NastaveniaButton" type="Button" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(320, 60)
theme_override_fonts/font = ExtResource("8_font")
theme_override_font_sizes/font_size = 24
theme_override_colors/font_color = Color(0.95, 0.9, 0.8, 1)
theme_override_colors/font_hover_color = Color(1, 0.98, 0.9, 1)
theme_override_colors/font_outline_color = Color(0.15, 0.08, 0.03, 1)
theme_override_constants/outline_size = 1
text = "NASTAVENIA"

[node name="OHreButton" type="Button" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(320, 60)
theme_override_fonts/font = ExtResource("8_font")
theme_override_font_sizes/font_size = 24
theme_override_colors/font_color = Color(0.95, 0.9, 0.8, 1)
theme_override_colors/font_hover_color = Color(1, 0.98, 0.9, 1)
theme_override_colors/font_outline_color = Color(0.15, 0.08, 0.03, 1)
theme_override_constants/outline_size = 1
text = "O HRE"

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
"": SubResource("Animation_fade_in")
}
