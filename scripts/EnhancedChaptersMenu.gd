extends Control

# Enhanced Chapters Menu s progress barmi a novými UI assetmi

@onready var progress_label = $MainContainer/VBoxContainer/HeaderSection/ProgressLabel
@onready var overall_progress_bar = $MainContainer/VBoxContainer/HeaderSection/OverallProgressBar
@onready var chapters_vbox = $MainContainer/VBoxContainer/ChaptersScrollContainer/ChaptersVBox
@onready var back_button = $MainContainer/VBoxContainer/ButtonsSection/BackButton
@onready var reset_button = $MainContainer/VBoxContainer/ButtonsSection/ResetButton

# Carousel premenné
var current_chapter_index: int = 0
var carousel_container: Control
var chapter_image: TextureRect
var chapter_title: Label
var chapter_description: Label
var chapter_button: Button
var prev_button: Button
var next_button: Button
var chapter_indicator: Label

# Definície kapitol
var chapters_data = [
	{
		"id": 1,
		"title": "Kapitola 1: <PERSON>ríchod do hradu",
		"description": "Van Helsing prichádza do z<PERSON>hadn<PERSON>ho hradu",
		"puzzles_count": 2,
		"scene": "res://scenes/Kapitola1.tscn"
	},
	{
		"id": 2,
		"title": "Kapitola 2: Prvé stretnutie",
		"description": "Stretnutie s tajomným majiteľom hradu",
		"puzzles_count": 2,
		"scene": "res://scenes/Kapitola2.tscn"
	},
	{
		"id": 3,
		"title": "Kapitola 3: Pátranie",
		"description": "Hľadanie stôp a záhad v hrade",
		"puzzles_count": 2,
		"scene": "res://scenes/Kapitola3.tscn"
	},
	{
		"id": 4,
		"title": "Kapitola 4: Nočné tajomstvá",
		"description": "Odhalenie temných tajomstiev",
		"puzzles_count": 2,
		"scene": "res://scenes/Kapitola4.tscn"
	},
	{
		"id": 5,
		"title": "Kapitola 5: Konfrontácia",
		"description": "Priama konfrontácia so zlom",
		"puzzles_count": 2,
		"scene": "res://scenes/Kapitola5.tscn"
	},
	{
		"id": 6,
		"title": "Kapitola 6: Finálny boj",
		"description": "Rozhodujúci súboj s upírom",
		"puzzles_count": 2,
		"scene": "res://scenes/Kapitola6.tscn"
	},
	{
		"id": 7,
		"title": "Epilóg: Nový úsvit",
		"description": "Záver príbehu a nová nádej",
		"puzzles_count": 0,
		"scene": "res://scenes/Epilog.tscn"
	}
]

func _ready():
	print("Enhanced Chapters Menu načítané")
	print("🔍 Kontrolujem uzly...")
	print("  - progress_label: ", progress_label != null)
	print("  - overall_progress_bar: ", overall_progress_bar != null)
	print("  - chapters_vbox: ", chapters_vbox != null)
	print("  - back_button: ", back_button != null)
	print("  - reset_button: ", reset_button != null)

	if progress_label == null or overall_progress_bar == null or chapters_vbox == null or back_button == null or reset_button == null:
		print("❌ Niektoré uzly neexistujú!")
		return

	print("✅ Všetky uzly existujú, pokračujem...")
	# Najprv len základné funkcie
	setup_buttons()
	update_progress_display()
	setup_carousel_chapters_menu()

func setup_simple_chapters_menu():
	"""Jednoduchá verzia menu kapitol bez komplexných assetov"""
	print("📚 Nastavujem jednoduché chapters menu...")

	# Vymazanie existujúcich kapitol (okrem prvej, ktorá je template)
	var children = chapters_vbox.get_children()
	for i in range(1, children.size()):
		children[i].queue_free()

	# Vytvorenie jednoduchých kapitol
	for chapter_data in chapters_data:
		create_simple_chapter_entry(chapter_data)

func setup_carousel_chapters_menu():
	"""Vytvorenie krásneho carousel menu s obrázkami kapitol"""
	print("🎠 Nastavujem carousel chapters menu...")

	# Vyčistíme existujúci obsah
	var children = chapters_vbox.get_children()
	for child in children:
		child.queue_free()

	# Vytvoríme carousel kontajner
	create_carousel_container()

	# Nastavíme prvú kapitolu
	update_carousel_display()

func create_carousel_container():
	"""Vytvorí hlavný kontajner pre carousel"""
	print("🏗️ Vytváram carousel kontajner...")

	# Hlavný carousel kontajner
	carousel_container = VBoxContainer.new()
	carousel_container.name = "CarouselContainer"
	chapters_vbox.add_child(carousel_container)

	# Sekcia s obrázkom a navigáciou
	var image_section = HBoxContainer.new()
	image_section.name = "ImageSection"
	image_section.alignment = BoxContainer.ALIGNMENT_CENTER
	carousel_container.add_child(image_section)

	# Predchádzajúce tlačidlo
	prev_button = Button.new()
	prev_button.text = "< Predch."
	prev_button.add_theme_font_size_override("font_size", 18)
	prev_button.custom_minimum_size = Vector2(100, 50)
	prev_button.pressed.connect(_on_prev_chapter)
	image_section.add_child(prev_button)

	# Obrázok kapitoly
	chapter_image = TextureRect.new()
	chapter_image.custom_minimum_size = Vector2(400, 300)
	chapter_image.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
	chapter_image.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
	image_section.add_child(chapter_image)

	# Nasledujúce tlačidlo
	next_button = Button.new()
	next_button.text = "Ďalšia >"
	next_button.add_theme_font_size_override("font_size", 18)
	next_button.custom_minimum_size = Vector2(100, 50)
	next_button.pressed.connect(_on_next_chapter)
	image_section.add_child(next_button)

	# Indikátor kapitoly
	chapter_indicator = Label.new()
	chapter_indicator.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	chapter_indicator.add_theme_font_size_override("font_size", 18)
	carousel_container.add_child(chapter_indicator)

	# Spacer
	var spacer = Control.new()
	spacer.custom_minimum_size = Vector2(0, 20)
	carousel_container.add_child(spacer)

	# Názov kapitoly
	chapter_title = Label.new()
	chapter_title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	chapter_title.add_theme_font_size_override("font_size", 28)
	carousel_container.add_child(chapter_title)

	# Popis kapitoly
	chapter_description = Label.new()
	chapter_description.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	chapter_description.add_theme_font_size_override("font_size", 16)
	chapter_description.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	carousel_container.add_child(chapter_description)

	# Spacer
	var spacer2 = Control.new()
	spacer2.custom_minimum_size = Vector2(0, 20)
	carousel_container.add_child(spacer2)

	# Tlačidlo na hranie
	chapter_button = Button.new()
	chapter_button.add_theme_font_size_override("font_size", 22)
	chapter_button.custom_minimum_size = Vector2(200, 50)
	carousel_container.add_child(chapter_button)

	print("✅ Carousel kontajner vytvorený")

func update_carousel_display():
	"""Aktualizuje zobrazenie carousel pre aktuálnu kapitolu"""
	if current_chapter_index < 0 or current_chapter_index >= chapters_data.size():
		return

	var chapter_data = chapters_data[current_chapter_index]
	print("🔄 Aktualizujem carousel pre kapitolu: ", chapter_data.title)

	# Načítanie obrázka
	var image_path = "res://assets/Kapitoly_VISUALS/Kapitola_%d.png" % chapter_data.id
	if ResourceLoader.exists(image_path):
		var texture = load(image_path)
		chapter_image.texture = texture
		print("✅ Obrázok načítaný: ", image_path)
	else:
		print("❌ Obrázok neexistuje: ", image_path)

	# Aktualizácia textov
	chapter_title.text = chapter_data.title
	chapter_description.text = chapter_data.description
	chapter_indicator.text = "Kapitola %d / %d" % [current_chapter_index + 1, chapters_data.size()]

	# Aktualizácia tlačidla
	chapter_button.text = "Vstúpiť do %s" % chapter_data.title.split(":")[1].strip_edges() if ":" in chapter_data.title else chapter_data.title

	# Odpojenie starých signálov a pripojenie nových
	if chapter_button.pressed.is_connected(_on_chapter_button_pressed):
		chapter_button.pressed.disconnect(_on_chapter_button_pressed)
	chapter_button.pressed.connect(_on_chapter_button_pressed.bind(chapter_data))

	# Aktualizácia navigačných tlačidiel
	prev_button.disabled = (current_chapter_index == 0)
	next_button.disabled = (current_chapter_index == chapters_data.size() - 1)

	print("✅ Carousel aktualizovaný")

func _on_prev_chapter():
	"""Prechod na predchádzajúcu kapitolu"""
	if current_chapter_index > 0:
		current_chapter_index -= 1
		update_carousel_display()
		if AudioManager:
			AudioManager.play_menu_button_sound()

func _on_next_chapter():
	"""Prechod na nasledujúcu kapitolu"""
	if current_chapter_index < chapters_data.size() - 1:
		current_chapter_index += 1
		update_carousel_display()
		if AudioManager:
			AudioManager.play_menu_button_sound()

func setup_chapters_menu():
	"""Vytvorenie menu kapitol s progress barmi"""
	# Vymazanie existujúcich kapitol (okrem prvej, ktorá je template)
	var children = chapters_vbox.get_children()
	for i in range(1, children.size()):
		children[i].queue_free()

	# Vytvorenie kapitol
	for chapter_data in chapters_data:
		create_chapter_entry(chapter_data)

func create_simple_chapter_entry(chapter_data: Dictionary):
	"""Jednoduchá verzia chapter entry bez komplexných assetov"""
	print("📖 Vytváram jednoduchú kapitolu: ", chapter_data.title)

	var chapter_container = VBoxContainer.new()
	chapter_container.name = "Chapter%dContainer" % chapter_data.id

	# Jednoduchý title
	var title_label = Label.new()
	title_label.text = chapter_data.title
	title_label.add_theme_font_size_override("font_size", 24)
	chapter_container.add_child(title_label)

	# Jednoduchý popis
	var desc_label = Label.new()
	desc_label.text = chapter_data.description
	desc_label.add_theme_font_size_override("font_size", 16)
	desc_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	chapter_container.add_child(desc_label)

	# Jednoduchý button
	var chapter_button = Button.new()
	chapter_button.text = "Vstúpiť do Kapitoly %d" % chapter_data.id
	chapter_button.add_theme_font_size_override("font_size", 20)
	chapter_button.pressed.connect(_on_chapter_button_pressed.bind(chapter_data))
	chapter_container.add_child(chapter_button)

	# Pridanie do hlavného kontajnera
	chapters_vbox.add_child(chapter_container)
	print("✅ Kapitola vytvorená: ", chapter_data.title)

func create_chapter_entry(chapter_data: Dictionary):
	"""Vytvorenie entry pre kapitolu s progress barom"""
	var chapter_container = MarginContainer.new()
	chapter_container.name = "Chapter%dContainer" % chapter_data.id
	
	# Štýl kontajnera
	var frame_style = StyleBoxTexture.new()
	var texture_path = "res://assets/useful_ui/decorative_frames/layer_88.png"
	print("🖼️ Načítavam textúru: ", texture_path)
	if ResourceLoader.exists(texture_path):
		frame_style.texture = load(texture_path)
		print("✅ Textúra načítaná úspešne")
	else:
		print("❌ Textúra neexistuje: ", texture_path)
		return
	frame_style.texture_margin_left = 20
	frame_style.texture_margin_top = 20
	frame_style.texture_margin_right = 20
	frame_style.texture_margin_bottom = 20
	chapter_container.add_theme_stylebox_override("panel", frame_style)
	
	# Margins
	chapter_container.add_theme_constant_override("margin_left", 15)
	chapter_container.add_theme_constant_override("margin_top", 15)
	chapter_container.add_theme_constant_override("margin_right", 15)
	chapter_container.add_theme_constant_override("margin_bottom", 15)
	
	# VBox pre obsah kapitoly
	var chapter_vbox = VBoxContainer.new()
	chapter_vbox.add_theme_constant_override("separation", 10)
	chapter_container.add_child(chapter_vbox)
	
	# Header s titulom a statusom
	var header_hbox = HBoxContainer.new()
	chapter_vbox.add_child(header_hbox)
	
	# Titulok kapitoly
	var title_label = Label.new()
	title_label.text = chapter_data.title
	title_label.add_theme_font_size_override("font_size", 24)
	title_label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	header_hbox.add_child(title_label)
	
	# Status kapitoly
	var status_label = Label.new()
	status_label.name = "StatusLabel"
	status_label.add_theme_font_size_override("font_size", 18)
	header_hbox.add_child(status_label)
	
	# Progress bar pre hlavolamy
	var progress_bar = ProgressBar.new()
	progress_bar.name = "ProgressBar"
	progress_bar.max_value = chapter_data.puzzles_count
	progress_bar.step = 1.0
	progress_bar.show_percentage = false
	chapter_vbox.add_child(progress_bar)
	
	# Popis kapitoly
	var description_label = Label.new()
	description_label.text = chapter_data.description
	description_label.add_theme_font_size_override("font_size", 16)
	description_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	chapter_vbox.add_child(description_label)
	
	# Tlačidlo pre spustenie kapitoly
	var chapter_button = Button.new()
	chapter_button.name = "ChapterButton"
	chapter_button.text = "▶️ Hrať Kapitolu %d" % chapter_data.id
	chapter_button.add_theme_font_size_override("font_size", 20)
	chapter_button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	chapter_button.pressed.connect(_on_chapter_button_pressed.bind(chapter_data))
	chapter_vbox.add_child(chapter_button)
	
	# Pridanie do hlavného kontajnera
	chapters_vbox.add_child(chapter_container)
	
	# Aktualizácia statusu kapitoly
	update_chapter_status(chapter_data.id, chapter_container)

func update_chapter_status(chapter_id: int, container: MarginContainer):
	"""Aktualizácia statusu a progresu kapitoly"""
	var status_label = container.get_node("Chapter%dVBox/Chapter%dHeader/StatusLabel" % [chapter_id, chapter_id])
	var progress_bar = container.get_node("Chapter%dVBox/ProgressBar" % chapter_id)
	var chapter_button = container.get_node("Chapter%dVBox/ChapterButton" % chapter_id)
	
	# Získanie progresu z GameManager
	var is_completed = GameManager.completed_chapters.has(chapter_id)
	var is_unlocked = GameManager.is_chapter_unlocked(chapter_id)
	var puzzles_completed = GameManager.get_chapter_puzzles_completed(chapter_id)
	var total_puzzles = chapters_data[chapter_id - 1].puzzles_count
	
	# Aktualizácia statusu
	if is_completed:
		status_label.text = "✅ Dokončené"
		status_label.modulate = Color.GREEN
	elif is_unlocked:
		status_label.text = "🔓 Odomknuté"
		status_label.modulate = Color.YELLOW
	else:
		status_label.text = "🔒 Zamknuté"
		status_label.modulate = Color.GRAY
	
	# Aktualizácia progress baru
	progress_bar.value = puzzles_completed
	if total_puzzles > 0:
		var progress_text = "%d/%d hlavolamov" % [puzzles_completed, total_puzzles]
		progress_bar.tooltip_text = progress_text
	
	# Aktivácia/deaktivácia tlačidla
	chapter_button.disabled = not is_unlocked

func setup_buttons():
	"""Nastavenie tlačidiel"""
	back_button.pressed.connect(_on_back_pressed)
	reset_button.pressed.connect(_on_reset_pressed)
	
	# Focus
	back_button.grab_focus()

func update_progress_display():
	"""Aktualizácia celkového progresu"""
	var completed_count = GameManager.completed_chapters.size()
	var total_count = chapters_data.size()
	
	progress_label.text = "Dokončené: %d/%d kapitol" % [completed_count, total_count]
	overall_progress_bar.value = completed_count
	overall_progress_bar.max_value = total_count

func _on_chapter_button_pressed(chapter_data: Dictionary):
	"""Spustenie kapitoly"""
	print("▶️ Spúšťam kapitolu %d" % chapter_data.id)
	if AudioManager:
		AudioManager.play_menu_button_sound()
	
	# Fade out efekt
	var tween = create_tween()
	tween.tween_property(self, "modulate", Color(1, 1, 1, 0), 0.5)
	tween.tween_callback(func(): GameManager.load_chapter(chapter_data.id))

func _on_back_pressed():
	"""Návrat do hlavného menu"""
	print("⬅️ Návrat do hlavného menu")
	if AudioManager:
		AudioManager.play_menu_button_sound()
	
	var tween = create_tween()
	tween.tween_property(self, "modulate", Color(1, 1, 1, 0), 0.3)
	tween.tween_callback(func(): get_tree().change_scene_to_file("res://scenes/EnhancedMainMenu.tscn"))

func _on_reset_pressed():
	"""Reset progresu hry"""
	print("🔄 Reset progresu")
	if AudioManager:
		AudioManager.play_menu_button_sound()
	
	# Potvrdenie resetu
	var confirmation = AcceptDialog.new()
	confirmation.dialog_text = "Naozaj chcete resetovať celý progress hry?\nTáto akcia sa nedá vrátiť späť!"
	confirmation.title = "Potvrdenie resetu"
	add_child(confirmation)
	confirmation.popup_centered()
	
	confirmation.confirmed.connect(_perform_reset)

func _perform_reset():
	"""Vykonanie resetu progresu"""
	GameManager.reset_all_progress()
	setup_chapters_menu()
	update_progress_display()
	print("✅ Progress resetovaný")
