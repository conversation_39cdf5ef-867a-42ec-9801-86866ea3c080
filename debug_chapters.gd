extends Control

func _ready():
	print("🐛 DEBUG: Spúšťam debug pre chapters menu")
	
	# Test 1: Existencia scén
	test_scene_existence()
	
	# Test 2: Načítanie scény
	test_scene_loading()
	
	# Test 3: GameManager
	test_game_manager()

func test_scene_existence():
	print("\n🔍 TEST 1: Existencia scén")
	var scenes = [
		"res://scenes/EnhancedChaptersMenu.tscn",
		"res://scenes/NewChaptersMenu.tscn",
		"res://scenes/ChaptersMenuTest.tscn"
	]
	
	for scene_path in scenes:
		if ResourceLoader.exists(scene_path):
			print("✅ ", scene_path, " - existuje")
		else:
			print("❌ ", scene_path, " - neexistuje")

func test_scene_loading():
	print("\n🔄 TEST 2: Načítanie EnhancedChaptersMenu")
	var scene_path = "res://scenes/EnhancedChaptersMenu.tscn"
	
	try:
		var scene_resource = load(scene_path)
		if scene_resource:
			print("✅ Scéna sa načítala")
			var scene_instance = scene_resource.instantiate()
			if scene_instance:
				print("✅ Inštancia sa vytvorila")
				# Skúsime pridať do stromu
				add_child(scene_instance)
				print("✅ Scéna sa pridala do stromu")
				# Odstránime ju hneď
				scene_instance.queue_free()
			else:
				print("❌ Nepodarilo sa vytvoriť inštanciu")
		else:
			print("❌ Nepodarilo sa načítať scénu")
	except:
		print("❌ Chyba pri načítavaní scény")

func test_game_manager():
	print("\n🎮 TEST 3: GameManager")
	if GameManager:
		print("✅ GameManager existuje")
		print("📚 Testujem GameManager.go_to_chapters()...")
		# Počkáme chvíľu a potom zavoláme
		await get_tree().create_timer(1.0).timeout
		GameManager.go_to_chapters()
	else:
		print("❌ GameManager neexistuje")

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		get_tree().change_scene_to_file("res://scenes/EnhancedMainMenu.tscn")
