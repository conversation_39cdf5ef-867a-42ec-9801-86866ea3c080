extends Control

@onready var back_button = $VBoxContainer/BackButton

func _ready():
	print("📚 Simple Chapters Menu načítané")
	setup_buttons()

func setup_buttons():
	if back_button:
		back_button.pressed.connect(_on_back_pressed)
		back_button.grab_focus()
	else:
		print("❌ Back button neexistuje!")

func _on_back_pressed():
	print("⬅️ Návrat do hlavného menu")
	if AudioManager:
		AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/EnhancedMainMenu.tscn")
